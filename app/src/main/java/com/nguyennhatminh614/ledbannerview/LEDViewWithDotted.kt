package com.nguyennhatminh614.ledbannerview

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat

class LEDViewWithDotted @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Paint objects for different layers
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val xferPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // Bitmaps and canvas for layers
    private var backgroundBitmap: Bitmap? = null
    private var textBitmap: Bitmap? = null
    private var xferBitmap: Bitmap? = null
    private var backgroundCanvas: Canvas? = null
    private var textCanvas: Canvas? = null
    
    // Text properties
    private var displayText = "LED Banner View 🚀✨"
    private var textSize = 80f
    private var textColor = Color.WHITE
    private var backgroundColor = Color.BLACK

    // Dot properties for LED effect
    private var dotRadius = 3f
    private var dotSpacing = 8
    private var dotBrightness = 1.0f
    private var useDotEffect = true
    
    // Animation properties
    private var textX = 0f
    private var textWidth = 0f
    private var animator: ValueAnimator? = null
    private var animationSpeed = 2000L // milliseconds for one cycle
    
    // Xfermode properties
    private var dotsBitmap: Bitmap? = null
    private val xferMode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)
    
    init {
        setupPaints()
        loadXferBitmap()
        // Initialize text position
        textX = 0f
    }
    
    private fun setupPaints() {
        // Setup text paint
        textPaint.apply {
            color = textColor
            textSize = <EMAIL>
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }
        
        // Setup background paint
        backgroundPaint.apply {
            color = backgroundColor
            style = Paint.Style.FILL
        }
        
        // Setup xfer paint
        xferPaint.apply {
            isAntiAlias = true
        }
    }
    
    private fun loadXferBitmap() {
        try {
            val drawable = ContextCompat.getDrawable(context, R.drawable.background_dots_xfer_for_android)
            drawable?.let {
                dotsBitmap = Bitmap.createBitmap(
                    it.intrinsicWidth,
                    it.intrinsicHeight,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(dotsBitmap!!)
                it.setBounds(0, 0, it.intrinsicWidth, it.intrinsicHeight)
                it.draw(canvas)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun startTextAnimation() {
        animator?.cancel()

        if (width > 0 && textWidth > 0) {
            animator = ValueAnimator.ofFloat(0f, 1f).apply {
                duration = animationSpeed
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                addUpdateListener { animation ->
                    val progress = animation.animatedValue as Float
                    // Start from right edge, move to left edge
                    textX = width.toFloat() - (textWidth + width) * progress
                    invalidate()
                }
            }
            animator?.start()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        if (w > 0 && h > 0) {
            // Create bitmaps for each layer
            backgroundBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            textBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            xferBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)

            backgroundCanvas = Canvas(backgroundBitmap!!)
            textCanvas = Canvas(textBitmap!!)

            // Calculate text width
            textWidth = textPaint.measureText(displayText)

            // Start animation
            startTextAnimation()
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val w = width
        val h = height
        
        if (w <= 0 || h <= 0 || backgroundBitmap == null || textBitmap == null) return
        
        // Clear all layers
        backgroundCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        textCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        
        // Layer 1: Draw background
        drawBackgroundLayer(backgroundCanvas!!, w, h)
        
        // Layer 2: Draw text
        drawTextLayer(textCanvas!!, w, h)
        
        // Layer 3: Apply xfermode effect
        drawXferLayer(canvas, w, h)
    }
    
    private fun drawBackgroundLayer(canvas: Canvas, width: Int, height: Int) {
        // Create gradient background
        val gradient = LinearGradient(
            0f, 0f, width.toFloat(), height.toFloat(),
            intArrayOf(Color.parseColor("#FF1744"), Color.parseColor("#FF5722"), Color.parseColor("#FF9800")),
            null,
            Shader.TileMode.CLAMP
        )
        backgroundPaint.shader = gradient
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), backgroundPaint)
    }
    
    private fun drawTextLayer(canvas: Canvas, width: Int, height: Int) {
        // Calculate text position
        val textY = height / 2f + textSize / 3f

        if (useDotEffect) {
            // Create dotted text effect
            drawDottedText(canvas, displayText, textX, textY)
        } else {
            // Draw normal text
            canvas.drawText(displayText, textX, textY, textPaint)
        }
    }

    private fun drawDottedText(canvas: Canvas, text: String, x: Float, y: Float) {
        // Create a temporary bitmap to render the text first
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)

        val tempWidth = textBounds.width() + 20
        val tempHeight = textBounds.height() + 20

        if (tempWidth <= 0 || tempHeight <= 0) return

        val tempBitmap = Bitmap.createBitmap(tempWidth, tempHeight, Bitmap.Config.ARGB_8888)
        val tempCanvas = Canvas(tempBitmap)

        // Draw text on temporary canvas preserving original colors (especially for emoji)
        val tempPaint = Paint(textPaint)
        // Don't override color - let emoji keep their natural colors
        tempCanvas.drawText(text, 10f, tempHeight - 10f, tempPaint)

        // Analyze pixels and draw dots
        val currentDotRadius = if (dotRadius > 0) dotRadius else textSize / 20f
        val currentDotSpacing = if (dotSpacing > 0) dotSpacing else (currentDotRadius * 2.5f).toInt()

        val dotPaint = Paint(Paint.ANTI_ALIAS_FLAG)

        // Sample pixels and draw dots
        for (pixelY in 0 until tempHeight step currentDotSpacing) {
            for (pixelX in 0 until tempWidth step currentDotSpacing) {
                val pixel = tempBitmap.getPixel(pixelX, pixelY)
                val alpha = Color.alpha(pixel)

                // If pixel has content (not transparent), draw a dot
                if (alpha > 128) { // Threshold for detecting text
                    val dotX = x + pixelX - 10f
                    val dotY = y + pixelY - tempHeight + 10f

                    // Extract the actual color from the pixel (important for emoji)
                    val red = Color.red(pixel)
                    val green = Color.green(pixel)
                    val blue = Color.blue(pixel)

                    val pixelColor = if (red == green && green == blue && red > 200) {
                        // White/light grayscale text - use original text color
                        textColor
                    } else if (red == green && green == blue) {
                        // Dark grayscale text - use original text color but darker
                        Color.argb(alpha,
                            (Color.red(textColor) * red / 255),
                            (Color.green(textColor) * green / 255),
                            (Color.blue(textColor) * blue / 255))
                    } else {
                        // Colored content (like emoji) - use the actual pixel color
                        Color.argb(alpha, red, green, blue)
                    }

                    dotPaint.color = pixelColor

                    // Add some variation to dot brightness for LED effect
                    val brightness = (alpha / 255f) * dotBrightness * 0.8f + 0.2f
                    dotPaint.alpha = (255 * brightness.coerceIn(0f, 1f)).toInt()

                    // Add slight glow effect
                    val glowPaint = Paint(dotPaint)
                    glowPaint.alpha = (dotPaint.alpha * 0.3f).toInt()
                    canvas.drawCircle(dotX, dotY, currentDotRadius * 1.5f, glowPaint)

                    // Draw main dot
                    canvas.drawCircle(dotX, dotY, currentDotRadius, dotPaint)
                }
            }
        }

        // Clean up
        tempBitmap.recycle()
    }
    
    private fun drawXferLayer(canvas: Canvas, width: Int, height: Int) {
        // First, draw background directly to main canvas
        canvas.drawBitmap(backgroundBitmap!!, 0f, 0f, null)

        // Then, draw text directly to main canvas
        canvas.drawBitmap(textBitmap!!, 0f, 0f, null)

        // Apply dots overlay with transparency for LED effect
        dotsBitmap?.let { dots ->
            val overlayPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            overlayPaint.alpha = 128 // 50% transparency for subtle effect

            // Scale dots to fit the view
            val scaleX = width.toFloat() / dots.width
            val scaleY = height.toFloat() / dots.height
            val scale = minOf(scaleX, scaleY) // Use min to maintain aspect ratio

            val matrix = Matrix()
            matrix.setScale(scale, scale)

            // Center the dots pattern
            val scaledWidth = dots.width * scale
            val scaledHeight = dots.height * scale
            val offsetX = (width - scaledWidth) / 2f
            val offsetY = (height - scaledHeight) / 2f
            matrix.postTranslate(offsetX, offsetY)

            // Draw dots overlay
            canvas.drawBitmap(dots, matrix, overlayPaint)
        }
    }
    
    // Public methods to control the LED view
    fun setText(text: String) {
        displayText = text
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextColor(color: Int) {
        textColor = color
        textPaint.color = color
        invalidate()
    }
    
    fun setTextSize(size: Float) {
        textSize = size
        textPaint.textSize = size
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setAnimationSpeed(speedMs: Long) {
        animationSpeed = speedMs
        if (animator?.isRunning == true) {
            startTextAnimation()
        }
    }
    
    fun startAnimation() {
        startTextAnimation()
    }
    
    fun stopAnimation() {
        animator?.cancel()
    }

    // Methods to customize dot effect
    fun setDotRadius(radius: Float) {
        dotRadius = radius
        invalidate()
    }

    fun setDotSpacing(spacing: Int) {
        dotSpacing = spacing
        invalidate()
    }

    fun setDotBrightness(brightness: Float) {
        dotBrightness = brightness.coerceIn(0f, 2f)
        invalidate()
    }

    fun setUseDotEffect(useDot: Boolean) {
        useDotEffect = useDot
        invalidate()
    }

    fun getDotRadius(): Float = dotRadius
    fun getDotSpacing(): Int = dotSpacing
    fun getDotBrightness(): Float = dotBrightness
    fun isUsingDotEffect(): Boolean = useDotEffect

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator?.cancel()
    }
}
