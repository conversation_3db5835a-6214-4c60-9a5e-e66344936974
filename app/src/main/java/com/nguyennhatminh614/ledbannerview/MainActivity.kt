package com.nguyennhatminh614.ledbannerview

import android.graphics.Color
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.SeekBar
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class MainActivity : AppCompatActivity() {

    private lateinit var ledView: LEDView
    private lateinit var editTextMessage: EditText
    private lateinit var btnUpdateText: Button
    private lateinit var btnStartStop: Button
    private lateinit var seekBarSpeed: SeekBar

    private var isAnimationRunning = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupListeners()
    }

    private fun initViews() {
        ledView = findViewById(R.id.ledView)
        editTextMessage = findViewById(R.id.editTextMessage)
        btnUpdateText = findViewById(R.id.btnUpdateText)
        btnStartStop = findViewById(R.id.btnStartStop)
        seekBarSpeed = findViewById(R.id.seekBarSpeed)
    }

    private fun setupListeners() {
        // Update text button
        btnUpdateText.setOnClickListener {
            val newText = editTextMessage.text.toString()
            if (newText.isNotEmpty()) {
                ledView.setText(newText)
            }
        }

        // Start/Stop animation button
        btnStartStop.setOnClickListener {
            if (isAnimationRunning) {
                ledView.stopAnimation()
                btnStartStop.text = "Chạy"
                isAnimationRunning = false
            } else {
                ledView.startAnimation()
                btnStartStop.text = "Dừng"
                isAnimationRunning = true
            }
        }

        // Speed control seekbar
        seekBarSpeed.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    // Convert progress (0-100) to speed (500ms - 5000ms)
                    val speed = 5000L - (progress * 45L) // 5000ms to 500ms
                    ledView.setAnimationSpeed(speed)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        ledView.stopAnimation()
    }
}